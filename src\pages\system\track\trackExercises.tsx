import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { Card, Breadcrumb, Select, Spin, Typography, Empty, Avatar, Tooltip } from 'antd';
import { HomeOutlined, UserOutlined, TeamOutlined } from '@ant-design/icons';
import { ReactFlow, Background, Controls, Handle, Position, type Node, type Edge } from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import dayjs from 'dayjs';
import { getClazzStudents, getClazz, type ClassStudentWithStudentInfoResponse, type ClazzResponse } from '../../../services/system/clazz';
import { getTrackExercises, type TrackExercise } from '../../../services/system/track';
import { showError } from '../../../utils/errorHandler';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';
import { Link, useParams } from 'react-router-dom';

const { Title, Text } = Typography;

// 自定义节点组件
interface ExerciseNodeData {
  exercise: TrackExercise;
  isEnabled: boolean;
  index: number;
  exercises: TrackExercise[];
  getTypeTag: (type: number) => React.ReactNode;
  getStatusTag: (status: number) => React.ReactNode;
  formatTime: (timeString: string | undefined) => string;
}
const ExerciseNode = ({ data }: { data: ExerciseNodeData }) => {
  const { exercise, isEnabled, index, exercises, getTypeTag, getStatusTag, formatTime } = data;

  return (
    <div style={{ position: 'relative' }}>
      {/* 输入句柄 - 除了第一个节点，其他节点都有输入句柄 */}
      {index > 0 && (
        <Handle
          type="target"
          position={Position.Left}
          style={{ background: '#b1b1b1', width: '0px', height: '0px', border: '2px solid #fff' }}
        />
      )}
      {/* 输出句柄 - 除了最后一个节点，其他节点都有输出句柄 */}
      {index < exercises.length - 1 && (
        <Handle
          type="source"
          position={Position.Right}
          style={{ background: '#b1b1b1', width: '0px', height: '0px', border: '2px solid #fff' }}
        />
      )}
      <Card
        hoverable={isEnabled}
        style={{
          width: '280px',
          opacity: isEnabled ? 1 : 0.6,
          filter: isEnabled ? 'none' : 'grayscale(50%)',
          position: 'relative',
          cursor: isEnabled ? 'pointer' : 'not-allowed',
        }}
        cover={
          exercise.e_pic ? (
            <img alt={exercise.e_title} src={exercise.e_pic} style={{ height: '160px', objectFit: 'cover' }} />
          ) : (
            <div style={{ height: '160px', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Avatar size={48} icon={<UserOutlined />} style={{ backgroundColor: 'rgba(255,255,255,0.2)' }} />
            </div>
          )
        }
      >
        <Card.Meta
          title={
            <div>
              <div style={{ position: 'absolute', top: '4px', left: '8px', zIndex: 5 }}>{getTypeTag(exercise.e_type)}</div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px', paddingTop: 'px' }}>
                <div style={{ display: 'flex', alignItems: 'center', flex: 1, paddingRight: '8px' }}>
                  <Text strong style={{ fontSize: '18px' }}>{exercise.e_title}</Text>
                </div>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {getStatusTag(typeof exercise.el_status === 'number' ? exercise.el_status : 0)}
                {exercise.el_status === 1 && exercise.el_utime && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>上次练习: {formatTime(exercise.el_utime)}</Text>
                )}
                {exercise.el_status === 2 && exercise.el_stime && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>提交时间: {formatTime(exercise.el_stime)}</Text>
                )}
              </div>
            </div>
          }
        />
        {/* 锁定图标 */}
        {!isEnabled && (
          <Tooltip title="请提交前一练习后，方能解锁！">
            <div style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', background: 'rgba(0, 0, 0, 0.7)', borderRadius: '50%', width: '60px', height: '60px', display: 'flex', alignItems: 'center', justifyContent: 'center', zIndex: 10 }}>
              <UserOutlined style={{ fontSize: '24px', color: '#fff' }} />
            </div>
          </Tooltip>
        )}
      </Card>
    </div>
  );
};

const nodeTypes = { exerciseNode: ExerciseNode };

const TrackExercises: React.FC = () => {
  const { classId } = useParams();
  const classIdNum = classId ? Number(classId) : undefined;
  const [students, setStudents] = useState<ClassStudentWithStudentInfoResponse[]>([]);
  const [selectedStudentId, setSelectedStudentId] = useState<number | undefined>(undefined);
  const [exercises, setExercises] = useState<TrackExercise[]>([]);
  const [loading, setLoading] = useState(false);
  const [studentSearch, setStudentSearch] = useState('');
  const [clazzInfo, setClazzInfo] = useState<ClazzResponse | null>(null);

  // 获取班级信息
  const fetchClazzInfo = useCallback(async () => {
    try {
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !classIdNum) return;
      const res = await getClazz(classIdNum, tenantInfo.id);
      setClazzInfo(res);
    } catch (e) {
      showError(e, '获取班级信息失败');
    }
  }, [classIdNum]);

  // 获取班级学员列表
  const fetchStudents = useCallback(async () => {
    try {
      setLoading(true);
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !classIdNum) return;
      const res = await getClazzStudents(classIdNum, tenantInfo.id, { active: 1, limit: 100 });
      setStudents(res.items);

      // 如果学员列表不为空且当前没有选中学员，则默认选择第一个学员
      if (res.items.length > 0 && !selectedStudentId) {
        setSelectedStudentId(res.items[0].sid);
      }
    } catch (e) {
      showError(e, '获取学员列表失败');
    } finally {
      setLoading(false);
    }
  }, [classIdNum, selectedStudentId]);

  // 获取学员练习计划跟踪
  const fetchTrackExercises = useCallback(async (studentId: number) => {
    try {
      setLoading(true);
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !classIdNum) return;
      const res = await getTrackExercises(classIdNum, studentId, tenantInfo.id);
      setExercises(res.exercises);
    } catch (e) {
      showError(e, '获取学员练习计划失败');
      setExercises([]);
    } finally {
      setLoading(false);
    }
  }, [classIdNum]);

  useEffect(() => {
    fetchClazzInfo();
    fetchStudents();
  }, [fetchClazzInfo, fetchStudents]);

  useEffect(() => {
    if (typeof selectedStudentId === 'number') {
      fetchTrackExercises(selectedStudentId);
    } else {
      setExercises([]);
    }
  }, [selectedStudentId, fetchTrackExercises]);

  // 练习类型标签
  const getTypeTag = useCallback((type: number) => {
    switch (type) {
      case 1:
        return <span style={{ color: '#1677ff', backgroundColor: '#e6f4ff', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>作业单</span>;
      case 2:
        return <span style={{ color: '#722ed1', backgroundColor: '#f9f0ff', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>角色扮演</span>;
      default:
        return <span style={{ color: '#aaa', backgroundColor: '#f5f5f5', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>未知类型</span>;
    }
  }, []);

  // 练习状态标签
  const getStatusTag = useCallback((status: number) => {
    switch (status) {
      case 0:
        return <span style={{ color: '#8c8c8c', backgroundColor: '#f5f5f5', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>待练习</span>;
      case 1:
        return <span style={{ color: '#21808d', backgroundColor: '#e6fffb', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>练习中</span>;
      case 2:
        return <span style={{ color: '#13343b', backgroundColor: '#f6ffed', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>已提交</span>;
      default:
        return <span style={{ color: '#8c8c8c', backgroundColor: '#f5f5f5', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>未开始</span>;
    }
  }, []);

  // 格式化时间
  const formatTime = useCallback((timeString: string | undefined) => {
    if (!timeString) return '';
    return dayjs(timeString).format('MM-DD HH:mm');
  }, []);

  // 判断练习是否可用
  const isExerciseEnabled = useCallback((exercise: TrackExercise, index: number) => {
    if (exercise.depend === 0) return true;
    if (exercise.depend === 1) {
      if (index === 0) return true;
      const prev = exercises[index - 1];
      return prev && prev.el_status === 2;
    }
    return true;
  }, [exercises]);

  // 生成流程图节点和边
  const { nodes, edges } = useMemo(() => {
    const nodes: Node[] = [];
    const edges: Edge[] = [];
    exercises.forEach((exercise, index) => {
      const isEnabled = isExerciseEnabled(exercise, index);
      const x = index * 400 + 100;
      const y = 200;
      const nodeId = `exercise-${exercise.e_id}`;
      nodes.push({
        id: nodeId,
        type: 'exerciseNode',
        position: { x, y },
        data: {
          exercise,
          index,
          isEnabled,
          exercises,
          getTypeTag,
          getStatusTag,
          formatTime,
        },
        draggable: false,
      });
      if (index < exercises.length - 1) {
        const next = exercises[index + 1];
        const sourceId = nodeId;
        const targetId = `exercise-${next.e_id}`;
        const isNextEnabled = isExerciseEnabled(next, index + 1);
        const strokeColor = isNextEnabled ? '#5f5f5f' : '#b1b1b1';
        edges.push({
          id: `edge-${exercise.e_id}-${next.e_id}`,
          source: sourceId,
          target: targetId,
          type: 'smoothstep',
          animated: true,
          style: { stroke: strokeColor, strokeWidth: 2, strokeDasharray: '5,5' },
        });
      }
    });
    return { nodes, edges };
  }, [exercises, getTypeTag, getStatusTag, formatTime, isExerciseEnabled]);

  // 学员下拉选项
  const filteredStudents = useMemo(() => {
    if (!studentSearch) return students;
    return students.filter(s => s.name.includes(studentSearch));
  }, [students, studentSearch]);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: 'calc(100vh - 180px)' }}>
      {/* 面包屑导航 */}
      <Breadcrumb
        style={{ marginBottom: 16, flexShrink: 0 }}
        items={[
          {
            title: <Link to="/system/home"><HomeOutlined /> 主页</Link>,
          },
          {
            title: '租户空间',
          },
          {
            title: <Link to="/system/track">教学跟踪</Link>,
          },
          {
            title: clazzInfo?.name || '班级练习跟踪',
          },
        ]}
      />
      <Card
        style={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden', hei }}
        styles={{ body: { flex: 1, display: 'flex', flexDirection: 'column', padding: 0, overflow: 'hidden' } }}
        title={<span><TeamOutlined /> {clazzInfo?.name || '班级练习跟踪'}</span>}
        extra={
          <Select
            showSearch
            allowClear
            placeholder="请选择学员"
            style={{ width: 260 }}
            value={selectedStudentId}
            searchValue={studentSearch}
            onChange={v => setSelectedStudentId(v)}
            onSearch={(value) => {
              setStudentSearch(value || '');
            }}
            onClear={() => {
              setStudentSearch('');
            }}
            filterOption={false}
            optionLabelProp="label"
            options={filteredStudents.map(s => ({
              value: s.sid,
              label: `${s.name}（ID: ${s.sid}）`,
            }))}
          />
        }
      >
        {loading ? (
          <div style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Spin size="large" />
          </div>
        ) : !selectedStudentId ? (
          <div style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Empty description="请先选择学员" />
          </div>
        ) : exercises.length === 0 ? (
          <div style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Empty description="暂无练习计划" />
          </div>
        ) : (
          <div style={{ flex: 1, width: '100%', position: 'relative' }}>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              nodeTypes={nodeTypes}
              fitView
              fitViewOptions={{ padding: 0.2, minZoom: 0.6, maxZoom: 1 }}
              nodesDraggable={false}
              nodesConnectable={false}
              elementsSelectable={true}
              panOnDrag={true}
              zoomOnScroll={true}
              zoomOnPinch={true}
              minZoom={0.6}
              maxZoom={1}
              proOptions={{ hideAttribution: true }}
              style={{ width: '100%', height: '100%', userSelect: 'none' }}
            >
              <Background />
              <Controls />
              {/* 左上角信息 */}
              <div style={{ position: 'absolute', top: '8px', left: '8px', zIndex: 1000, background: 'rgba(255,255,255,0.9)', padding: '8px', borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)', userSelect: 'none' }}>
                <Title level={4} style={{ margin: '8px', fontSize: '14px' }}>
                  练习计划如下：
                </Title>
              </div>
            </ReactFlow>
          </div>
        )}
      </Card>
    </div>
  );
};

export default TrackExercises; 